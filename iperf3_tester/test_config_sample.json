{"network_config": {"remote_ip": "*******", "port_range": {"start": 52001, "end": 52002}, "protocol": "tcp", "bandwidth": "1M", "packet_size": "128K", "tos": 0, "client_only": true}, "test_parameters": {"test_duration": 5, "parallel_streams": [1], "protocols": ["tcp"], "directions": ["tx"], "message_sizes": [null], "window_sizes": [null], "reporting_interval": 1, "warmup_time": 0, "client_options": []}, "execution_config": {"max_retries": 1, "retry_delay": 2, "timeout_seconds": 30, "iperf3_path": "iperf3", "max_concurrent_clients_per_iteration": 1}, "output_config": {"log_file": "iperf3_test.log", "results_format": "all", "results_directory": "results", "save_to_file": true, "verbosity": 1, "json_report_filename": "iperf3_results_sample.json", "csv_report_filename": "iperf3_results_sample.csv"}}