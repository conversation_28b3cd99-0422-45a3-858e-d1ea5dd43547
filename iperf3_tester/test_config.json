{"network_config": {"remote_ip": "*******", "port_range": {"start": 52001, "end": 52016}, "protocol": "tcp", "bandwidth": "1M", "packet_size": "128K", "tos": 0, "ssh_user": "root", "ssh_password": "Admin@12"}, "test_parameters": {"test_duration": 10, "parallel_streams": [1], "protocols": ["tcp", "udp"], "directions": ["tx", "rx", "bx"], "message_sizes": ["1K", "2K", "4K", "8K", "16K", "32K", "64K"], "window_sizes": [null], "reporting_interval": 1, "warmup_time": 3, "udp_bandwidth": "100G", "client_options": []}, "execution_config": {"max_retries": 3, "retry_delay": 5, "timeout_seconds": 60, "iperf3_path": "iperf3", "max_concurrent_clients_per_iteration": 0}, "output_config": {"log_file": "iperf3_test.log", "results_format": "all", "results_directory": "results", "save_to_file": true, "verbosity": 1, "json_report_filename": "iperf3_results.json", "csv_report_filename": "iperf3_results.csv"}}